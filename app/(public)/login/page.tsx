"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { <PERSON><PERSON>eft, Shield, Zap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useAuthStore } from "@/stores/auth-store";
import Image from "next/image";
import { LoginForm } from "@/components/auth/login-form";
import { RegisterForm } from "@/components/auth/register-form";
import { GoogleAuth } from "@/components/auth/google-auth";

export default function LoginPage() {
    const router = useRouter();
    const { user, isLoaded } = useAuthStore();
    const [isLogin, setIsLogin] = useState(true);

    // Redirect if user is already logged in
    useEffect(() => {
        if (isLoaded && user) {
            router.push("/dashboard");
        }
    }, [isLoaded, user, router]);




    return (


        <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute -top-40 -right-32 w-80 h-80 rounded-full bg-gradient-to-r from-blue-400/20 to-indigo-400/20 blur-3xl opacity-70"></div>
                <div className="absolute -bottom-40 -left-32 w-80 h-80 rounded-full bg-gradient-to-r from-purple-400/20 to-blue-400/20 blur-3xl opacity-70"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-indigo-400/10 to-purple-400/10 blur-3xl opacity-50"></div>
            </div>

            {/* Main content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen px-4">

                <div className="w-full max-w-md">
                    {/* Back to Home Button */}
                    <div className="flex items-center justify-start mb-8">
                        <Link href="/">
                            <Button
                                variant="ghost"
                                className="flex items-center space-x-2 text-slate-600 hover:text-blue-600 transition-colors duration-200 group"
                            >
                                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" />
                                <span className="font-medium">Back to Home</span>
                            </Button>
                        </Link>
                    </div>

                    {/* Logo and branding */}
                    <div className="text-center mb-10">
                        <div className="flex items-center justify-center space-x-3 mb-6">
                            <Link
                                href="/"
                                className="flex items-center gap-2 relative z-10"
                            >
                                {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
                                      <span className="text-white font-bold text-sm lg:text-base">
                                        P
                                      </span>
                                    </div> */}
                                <Image src="/icon.svg" alt="logo" width={32} height={32} />
                                <span className="font-bold text-xl lg:text-2xl gradient-text">
                                    Profolify
                                </span>
                            </Link>
                        </div>
                        <h2 className="text-3xl font-bold text-slate-800 mb-4">
                            Welcome Back
                        </h2>
                        <p className="text-slate-600 text-lg">
                            Sign in to continue building your professional portfolio
                        </p>
                    </div>

                    {/* Auth card */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-2xl">
                        {/* Auth form content */}
                        {isLogin ? (
                            <>
                                <LoginForm onSwitchToRegister={() => setIsLogin(false)} />
                                <GoogleAuth />
                            </>
                        ) : (
                            <>
                                <RegisterForm onSwitchToLogin={() => setIsLogin(true)} />
                                <GoogleAuth />
                            </>
                        )}

                        {/* Trust indicators */}
                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <div className="grid grid-cols-1 gap-4">
                                <div className="flex items-center space-x-3 p-4 rounded-xl bg-blue-50/50 border border-blue-100">
                                    <div className="flex-shrink-0">
                                        <Shield className="w-5 h-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm font-semibold text-slate-800">
                                            Secure Authentication
                                        </p>
                                        <p className="text-xs text-slate-600">
                                            Your data is protected with industry-standard security
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-3 p-4 rounded-xl bg-indigo-50/50 border border-indigo-100">
                                    <div className="flex-shrink-0">
                                        <Zap className="w-5 h-5 text-indigo-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm font-semibold text-slate-800">
                                            Instant Access
                                        </p>
                                        <p className="text-xs text-slate-600">
                                            Start building your portfolio immediately
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer text */}
                    <p className="text-center text-sm text-slate-500 mt-8">
                        By signing in, you agree to our{" "}
                        <button type="button" className="text-blue-600 hover:underline font-medium">
                            Terms of Service
                        </button>{" "}
                        and{" "}
                        <button type="button" className="text-blue-600 hover:underline font-medium">
                            Privacy Policy
                        </button>
                    </p>
                </div>
            </div >
        </div >
    );
}
