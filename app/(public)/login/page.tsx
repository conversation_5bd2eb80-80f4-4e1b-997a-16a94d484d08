"use client";

import { auth } from "@/lib/firebase";
import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect, useState } from "react";

import Loader from "@/components/ui/loader";
import { ArrowLeft, Shield, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useAuthStore } from "@/stores/auth-store";
import Image from "next/image";
import { EmailAuthForm } from "@/components/auth/email-auth-form";

// Helper function to set a cookie
const setCookie = (name: string, value: string, days: number) => {
    let expires = "";
    if (days) {
        const date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "") + expires + "; path=/";
};

export default function LoginPage() {
    const router = useRouter();
    const { user, isLoaded } = useAuthStore();
    const [isSigningIn, setIsSigningIn] = useState(false);
    const [authMethod, setAuthMethod] = useState<"email" | "google">("email");

    const handleSignIn = async () => {
        const provider = new GoogleAuthProvider();
        try {
            setIsSigningIn(true);
            const result = await signInWithPopup(auth, provider);
            const token = await result.user.getIdToken();

            if (token) {
                // Set cookie for middleware to read on subsequent requests
                setCookie("firebaseIdToken", token, 1);
            }

            // Navigate to dashboard - welcome message will be shown there
            router.push("/dashboard");

        } catch (error) {
            console.error("Error signing in with Google: ", error);
            toast.error("Failed to sign in. Please try again.");
        } finally {
            setIsSigningIn(false);
        }
    };

    // Redirect if user is already logged in (but not during sign-in process)
    useEffect(() => {
        if (isLoaded && user && !isSigningIn) {
            router.push("/dashboard");
        }
    }, [isLoaded, user, router, isSigningIn]);




    return (


        <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute -top-40 -right-32 w-80 h-80 rounded-full bg-gradient-to-r from-blue-400/20 to-indigo-400/20 blur-3xl opacity-70"></div>
                <div className="absolute -bottom-40 -left-32 w-80 h-80 rounded-full bg-gradient-to-r from-purple-400/20 to-blue-400/20 blur-3xl opacity-70"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-indigo-400/10 to-purple-400/10 blur-3xl opacity-50"></div>
            </div>

            {/* Main content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen px-4">

                <div className="w-full max-w-md">
                    {/* Back to Home Button */}
                    <div className="flex items-center justify-start mb-8">
                        <Link href="/">
                            <Button
                                variant="ghost"
                                className="flex items-center space-x-2 text-slate-600 hover:text-blue-600 transition-colors duration-200 group"
                            >
                                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" />
                                <span className="font-medium">Back to Home</span>
                            </Button>
                        </Link>
                    </div>

                    {/* Logo and branding */}
                    <div className="text-center mb-10">
                        <div className="flex items-center justify-center space-x-3 mb-6">
                            <Link
                                href="/"
                                className="flex items-center gap-2 relative z-10"
                            >
                                {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
                                      <span className="text-white font-bold text-sm lg:text-base">
                                        P
                                      </span>
                                    </div> */}
                                <Image src="/icon.svg" alt="logo" width={32} height={32} />
                                <span className="font-bold text-xl lg:text-2xl gradient-text">
                                    Profolify
                                </span>
                            </Link>
                        </div>
                        <h2 className="text-3xl font-bold text-slate-800 mb-4">
                            Welcome Back
                        </h2>
                        <p className="text-slate-600 text-lg">
                            Sign in to continue building your professional portfolio
                        </p>
                    </div>

                    {/* Sign in card */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-2xl">
                        {/* Tabs for auth methods */}
                        <div className="flex border-b border-gray-200 mb-6">
                            <button
                                type="button"
                                onClick={() => setAuthMethod("email")}
                                className={`flex-1 py-3 text-center font-medium text-sm transition-colors ${
                                    authMethod === "email"
                                        ? "text-blue-600 border-b-2 border-blue-600"
                                        : "text-gray-500 hover:text-gray-700"
                                }`}
                            >
                                Email & Password
                            </button>
                            <button
                                type="button"
                                onClick={() => setAuthMethod("google")}
                                className={`flex-1 py-3 text-center font-medium text-sm transition-colors ${
                                    authMethod === "google"
                                        ? "text-blue-600 border-b-2 border-blue-600"
                                        : "text-gray-500 hover:text-gray-700"
                                }`}
                            >
                                Google
                            </button>
                        </div>

                        {/* Auth method content */}
                        {authMethod === "email" ? (
                            <EmailAuthForm />
                        ) : (
                            <>
                                {/* Google Sign In Button */}
                                <button
                                    type="button"
                                    onClick={handleSignIn}
                                    disabled={isSigningIn}
                                    className="w-full flex items-center justify-center space-x-3 px-6 py-4 bg-white hover:bg-gray-50 border border-gray-200 rounded-2xl font-semibold text-gray-700 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 mb-6 shadow-lg"
                                >
                                    {isSigningIn ? (
                                        <Loader text="Signing in..." />
                                    ) : (
                                        <>
                                            <svg className="w-5 h-5" viewBox="0 0 24 24">
                                                <path
                                                    fill="#4285F4"
                                                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                                />
                                                <path
                                                    fill="#34A853"
                                                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                                />
                                                <path
                                                    fill="#FBBC05"
                                                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                                />
                                                <path
                                                    fill="#EA4335"
                                                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                                />
                                            </svg>
                                            <span>Continue with Google</span>
                                        </>
                                    )}
                                </button>
                            </>
                        )}

                        {/* Divider */}
                        <div className="relative my-6">
                            <div className="absolute inset-0 flex items-center">
                                <div className="w-full border-t border-gray-200"></div>
                            </div>
                            <div className="relative flex justify-center text-sm">
                                <span className="px-4 bg-white text-slate-500 font-medium">
                                    Secure & Trusted
                                </span>
                            </div>
                        </div>

                        {/* Trust indicators */}
                        <div className="grid grid-cols-1 gap-4">
                            <div className="flex items-center space-x-3 p-4 rounded-xl bg-blue-50/50 border border-blue-100">
                                <div className="flex-shrink-0">
                                    <Shield className="w-5 h-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-semibold text-slate-800">
                                        Secure Authentication
                                    </p>
                                    <p className="text-xs text-slate-600">
                                        Your data is protected with industry-standard security
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3 p-4 rounded-xl bg-indigo-50/50 border border-indigo-100">
                                <div className="flex-shrink-0">
                                    <Zap className="w-5 h-5 text-indigo-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-semibold text-slate-800">
                                        Instant Access
                                    </p>
                                    <p className="text-xs text-slate-600">
                                        Start building your portfolio immediately
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer text */}
                    <p className="text-center text-sm text-slate-500 mt-8">
                        By signing in, you agree to our{" "}
                        <button type="button" className="text-blue-600 hover:underline font-medium">
                            Terms of Service
                        </button>{" "}
                        and{" "}
                        <button type="button" className="text-blue-600 hover:underline font-medium">
                            Privacy Policy
                        </button>
                    </p>
                </div>
            </div >
        </div >
    );
}
