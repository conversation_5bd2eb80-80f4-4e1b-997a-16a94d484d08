"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Mail, CheckCircle, RefreshCw, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { useAuthStore } from "@/stores/auth-store";
import { sendVerificationEmail } from "@/lib/auth-utils";

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuthStore();
  const [email, setEmail] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  const handleResendVerification = async () => {
    if (!user) {
      toast.error("Please sign in again to resend verification email");
      router.push("/login");
      return;
    }

    setIsResending(true);
    try {
      const { success, error } = await sendVerificationEmail(user);
      
      if (success) {
        toast.success("Verification email sent! Please check your inbox");
      } else {
        toast.error(error || "Failed to send verification email");
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email";
      toast.error(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  const handleCheckVerification = async () => {
    if (!user) {
      toast.error("Please sign in again");
      router.push("/login");
      return;
    }

    setIsChecking(true);
    try {
      // Reload user to get latest email verification status
      await user.reload();
      
      if (user.emailVerified) {
        toast.success("Email verified successfully!");
        router.push("/dashboard");
      } else {
        toast.info("Email not yet verified. Please check your inbox and click the verification link");
      }
    } catch (error: unknown) {
      toast.error("Failed to check verification status");
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-32 w-80 h-80 rounded-full bg-gradient-to-r from-blue-400/20 to-indigo-400/20 blur-3xl opacity-70"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 rounded-full bg-gradient-to-r from-purple-400/20 to-blue-400/20 blur-3xl opacity-70"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-indigo-400/10 to-purple-400/10 blur-3xl opacity-50"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
        <div className="w-full max-w-md">
          {/* Back to Login Button */}
          <div className="flex items-center justify-start mb-8">
            <Link href="/login">
              <Button
                variant="ghost"
                className="flex items-center space-x-2 text-slate-600 hover:text-blue-600 transition-colors duration-200 group"
              >
                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" />
                <span className="font-medium">Back to Login</span>
              </Button>
            </Link>
          </div>

          {/* Logo and branding */}
          <div className="text-center mb-10">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <Link
                href="/"
                className="flex items-center gap-2 relative z-10"
              >
                <Image src="/icon.svg" alt="logo" width={32} height={32} />
                <span className="font-bold text-xl lg:text-2xl gradient-text">
                  Profolify
                </span>
              </Link>
            </div>
            <h2 className="text-3xl font-bold text-slate-800 mb-4">
              Verify Your Email
            </h2>
            <p className="text-slate-600 text-lg">
              We&apos;ve sent a verification link to your email address
            </p>
          </div>

          {/* Verification card */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-2xl">
            <div className="text-center space-y-6">
              {/* Email icon */}
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <Mail className="w-8 h-8 text-blue-600" />
              </div>

              {/* Email address */}
              <div>
                <p className="text-sm text-gray-600 mb-2">Verification email sent to:</p>
                <p className="font-semibold text-gray-900 break-all">{email}</p>
              </div>

              {/* Instructions */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-blue-800 mb-2">
                      Next Steps:
                    </h3>
                    <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                      <li>Check your email inbox (and spam folder)</li>
                      <li>Click the verification link in the email</li>
                      <li>Return here and click &quot;I&apos;ve Verified&quot;</li>
                    </ol>
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="space-y-3">
                <Button
                  onClick={handleCheckVerification}
                  disabled={isChecking}
                  className="w-full"
                >
                  {isChecking ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      I&apos;ve Verified My Email
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={handleResendVerification}
                  disabled={isResending}
                  className="w-full"
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4 mr-2" />
                      Resend Verification Email
                    </>
                  )}
                </Button>
              </div>

              {/* Help text */}
              <div className="text-xs text-gray-500">
                <p>Didn&apos;t receive the email? Check your spam folder or try resending.</p>
                <p className="mt-1">
                  Need help?{" "}
                  <Link href="/contact" className="text-blue-600 hover:underline">
                    Contact support
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
