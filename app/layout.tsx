import type { Metada<PERSON> } from "next";
import "./globals.css"; // Your globals.css should be imported here
// Import compiled theme CSS files (contains latest component styles)
import "../themes/modern/modern-modular.css";
import "../themes/creative-minimalist/creative-minimalist-compiled.css";//since this theme was not build automatically need to import it manually the compiled version
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/contexts/theme-provider";
import { GeistSans } from "geist/font/sans"; // Import the sans-serif font
import { Geist<PERSON><PERSON> } from "geist/font/mono"; // Import the monospaced font
import { AuthProvider } from "@/contexts/auth-provider";
import { QueryProvider } from "@/contexts/query-provider";


export const metadata: Metadata = {
  title: "Free Portfolio Builder | Create Professional Portfolios & Export Static HTML - Profolify",
  description: "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
  keywords: [
    // Primary keywords
    "portfolio builder", "free portfolio builder", "online portfolio builder", "professional portfolio builder",
    "portfolio maker", "portfolio creator", "portfolio website builder", "digital portfolio builder",

    // Export-related keywords
    "export portfolio HTML", "static HTML portfolio", "download portfolio website", "portfolio HTML export",
    "static website generator", "HTML portfolio generator", "export website HTML", "portfolio to HTML",

    // Professional/career keywords
    "professional portfolio", "career portfolio", "work portfolio", "creative portfolio",
    "developer portfolio", "designer portfolio", "freelancer portfolio", "personal portfolio website",

    // Resume/CV keywords
    "online resume builder", "resume portfolio", "CV portfolio", "resume website",
    "professional resume", "digital resume", "resume maker", "CV builder",

    // Theme/design keywords
    "portfolio themes", "portfolio templates", "responsive portfolio", "mobile portfolio",
    "modern portfolio design", "minimalist portfolio", "portfolio showcase",

    // Technical keywords
    "no code portfolio", "drag and drop portfolio", "WYSIWYG portfolio editor",
    "instant portfolio", "quick portfolio builder", "easy portfolio maker"
  ],
  authors: [{ name: "Profolify", url: "https://profolify.com" }],
  creator: "Profolify - Free Portfolio Builder",
  publisher: "Profolify",
  category: "Web Development Tools",
  classification: "Portfolio Builder Software",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
    other: [
      {
        rel: "android-chrome-192x192",
        url: "/android-chrome-192x192.png",
      },
      {
        rel: "android-chrome-512x512",
        url: "/android-chrome-512x512.png",
      },
    ],
  },
  manifest: "/site.webmanifest",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://profolify.com",
    title: "Free Portfolio Builder | Create Professional Portfolios & Export Static HTML",
    description: "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
    siteName: "Profolify - Free Portfolio Builder",
    images: [
      {
        url: "https://profolify.com/og-portfolio-builder.png",
        width: 1200,
        height: 630,
        alt: "Profolify - Free Portfolio Builder with HTML Export",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@profolify",
    creator: "@profolify",
    title: "Free Portfolio Builder | Create Professional Portfolios & Export Static HTML",
    description: "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
    images: ["https://profolify.com/og-portfolio-builder.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://profolify.com",
  },
  // Note: Add your verification codes after setting up Search Console
  // other: {
  //   "google-site-verification": "your-google-site-verification-code",
  //   "msvalidate.01": "your-bing-verification-code",
  // },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Profolify - Free Portfolio Builder",
    "description": "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
    "url": "https://profolify.com",
    "applicationCategory": "WebApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "creator": {
      "@type": "Organization",
      "name": "Profolify",
      "url": "https://profolify.com"
    },
    "featureList": [
      "Free Portfolio Builder",
      "Static HTML Export",
      "Professional Themes",
      "Mobile Responsive Design",
      "SEO Optimized Portfolios",
      "No Coding Required",
      "WYSIWYG Editor",
      "Instant Publishing"
    ],
    "screenshot": "https://profolify.com/app-screenshot.png",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150"
    }
  };

  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`} suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#8B5CF6" />
        <meta name="msapplication-TileColor" content="#8B5CF6" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />

        {/* Google Analytics */}
        {process.env.NODE_ENV === 'production' && (
          <>
            <script async src="https://www.googletagmanager.com/gtag/js?id=G-Q1GGSNZ1DV"></script>
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', 'G-Q1GGSNZ1DV');
                `,
              }}
            />
          </>
        )}

        {/* Additional SEO Meta Tags */}
        <meta name="application-name" content="Profolify" />
        <meta name="apple-mobile-web-app-title" content="Profolify" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="mobile-web-app-capable" content="yes" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://res.cloudinary.com" />
      </head>
      <body >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <AuthProvider>
            <QueryProvider>
              {children}
              <Toaster />
            </QueryProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}