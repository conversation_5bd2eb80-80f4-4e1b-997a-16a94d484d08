"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { setCookie } from "cookies-next";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/ui/loader";
import { 
  createUserWithEmail, 
  signInWithEmail, 
  resetPassword, 
  isValidEmail, 
  validatePassword 
} from "@/lib/auth-utils";

type AuthMode = "login" | "register" | "reset";

interface EmailAuthFormProps {
  defaultMode?: AuthMode;
  onSuccess?: () => void;
}

export function EmailAuthForm({ defaultMode = "login", onSuccess }: EmailAuthFormProps) {
  const router = useRouter();
  const [mode, setMode] = useState<AuthMode>(defaultMode);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!email || !isValidEmail(email)) {
      newErrors.push("Please enter a valid email address");
    }

    if (mode !== "reset") {
      if (!password) {
        newErrors.push("Password is required");
      } else if (mode === "register") {
        const passwordValidation = validatePassword(password);
        if (!passwordValidation.isValid) {
          newErrors.push(...passwordValidation.errors);
        }

        if (password !== confirmPassword) {
          newErrors.push("Passwords do not match");
        }

        if (!displayName) {
          newErrors.push("Name is required");
        }
      }
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors([]);

    try {
      if (mode === "login") {
        const { user, success, error } = await signInWithEmail(email, password);
        
        if (success && user) {
          const token = await user.getIdToken();
          setCookie("firebaseIdToken", token, { maxAge: 60 * 60 * 24 }); // 24 hours
          
          toast.success("Signed in successfully");
          
          if (onSuccess) {
            onSuccess();
          } else {
            router.push("/dashboard");
          }
        } else {
          setErrors([error || "Failed to sign in"]);
        }
      } else if (mode === "register") {
        const { user, success, error } = await createUserWithEmail(email, password);
        
        if (success && user) {
          const token = await user.getIdToken();
          setCookie("firebaseIdToken", token, { maxAge: 60 * 60 * 24 }); // 24 hours
          
          toast.success("Account created! Please verify your email");
          
          if (onSuccess) {
            onSuccess();
          } else {
            router.push("/dashboard");
          }
        } else {
          setErrors([error || "Failed to create account"]);
        }
      } else if (mode === "reset") {
        const { success, error } = await resetPassword(email);
        
        if (success) {
          toast.success("Password reset email sent");
          setMode("login");
        } else {
          setErrors([error || "Failed to send password reset email"]);
        }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setErrors([errorMessage]);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <ul className="list-disc pl-5 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isSubmitting}
            required
          />
        </div>

        {mode !== "reset" && (
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>
        )}

        {mode === "register" && (
          <>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={isSubmitting}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="displayName">Full Name</Label>
              <Input
                id="displayName"
                type="text"
                placeholder="John Doe"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                disabled={isSubmitting}
                required
              />
            </div>
          </>
        )}

        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Loader text={
              mode === "login" 
                ? "Signing in..." 
                : mode === "register" 
                  ? "Creating account..." 
                  : "Sending reset email..."
            } />
          ) : (
            mode === "login" 
              ? "Sign In" 
              : mode === "register" 
                ? "Create Account" 
                : "Reset Password"
          )}
        </Button>
      </form>

      <div className="text-center space-y-2">
        {mode === "login" && (
          <>
            <button
              type="button"
              onClick={() => setMode("reset")}
              className="text-sm text-blue-600 hover:underline"
            >
              Forgot password?
            </button>
            <div className="text-sm">
              Don&apos;t have an account?{" "}
              <button
                type="button"
                onClick={() => setMode("register")}
                className="text-blue-600 hover:underline font-medium"
              >
                Sign up
              </button>
            </div>
          </>
        )}

        {mode === "register" && (
          <div className="text-sm">
            Already have an account?{" "}
            <button
              type="button"
              onClick={() => setMode("login")}
              className="text-blue-600 hover:underline font-medium"
            >
              Sign in
            </button>
          </div>
        )}

        {mode === "reset" && (
          <div className="text-sm">
            Remember your password?{" "}
            <button
              type="button"
              onClick={() => setMode("login")}
              className="text-blue-600 hover:underline font-medium"
            >
              Sign in
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
