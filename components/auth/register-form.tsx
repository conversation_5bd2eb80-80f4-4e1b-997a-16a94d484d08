"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Eye, EyeOff } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/ui/loader";
import { 
  createUserWithEmail, 
  isValidEmail, 
  validatePassword 
} from "@/lib/auth-utils";

interface RegisterFormProps {
  onSwitchToLogin: () => void;
}

export function RegisterForm({ onSwitchToLogin }: RegisterFormProps) {
  const router = useRouter();
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!fullName.trim()) {
      newErrors.push("Full name is required");
    }

    if (!email || !isValidEmail(email)) {
      newErrors.push("Please enter a valid email address");
    }

    if (!password) {
      newErrors.push("Password is required");
    } else {
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        newErrors.push(...passwordValidation.errors);
      }
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors([]);

    try {
      const { user, success, error } = await createUserWithEmail(email, password);
      
      if (success && user) {
        // Update the user's display name
        await user.updateProfile({ displayName: fullName });
        
        toast.success("Account created successfully!");
        
        // Redirect to verification page with email
        router.push(`/verify-email?email=${encodeURIComponent(email)}`);
      } else {
        setErrors([error || "Failed to create account"]);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setErrors([errorMessage]);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get password strength indicator
  const getPasswordStrength = (password: string) => {
    if (!password) return null;
    
    const validation = validatePassword(password);
    const strength = validation.isValid ? "strong" : 
                    password.length >= 8 ? "medium" : "weak";
    
    return {
      strength,
      isValid: validation.isValid,
      errors: validation.errors
    };
  };

  const passwordStrength = getPasswordStrength(password);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900">Create Account</h3>
        <p className="text-sm text-gray-600 mt-1">
          Join us and start building your professional portfolio
        </p>
      </div>

      <form onSubmit={handleRegister} className="space-y-4">
        {errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <ul className="list-disc pl-5 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name</Label>
          <Input
            id="fullName"
            type="text"
            placeholder="John Doe"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            disabled={isSubmitting}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isSubmitting}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
          
          {/* Password Strength Indicator */}
          {passwordStrength && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className={`h-2 w-8 rounded ${
                    passwordStrength.strength === "weak" ? "bg-red-500" :
                    passwordStrength.strength === "medium" ? "bg-yellow-500" :
                    "bg-green-500"
                  }`} />
                  <div className={`h-2 w-8 rounded ${
                    passwordStrength.strength === "medium" ? "bg-yellow-500" :
                    passwordStrength.strength === "strong" ? "bg-green-500" :
                    "bg-gray-200"
                  }`} />
                  <div className={`h-2 w-8 rounded ${
                    passwordStrength.strength === "strong" ? "bg-green-500" : "bg-gray-200"
                  }`} />
                </div>
                <span className="text-sm text-gray-600 capitalize">
                  {passwordStrength.strength} password
                </span>
              </div>
              
              {passwordStrength.errors.length > 0 && (
                <div className="text-xs text-gray-600">
                  <p className="font-medium mb-1">Password must have:</p>
                  <ul className="space-y-1">
                    {passwordStrength.errors.map((error, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <span className="text-red-500">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Loader text="Creating account..." />
          ) : (
            "Create Account"
          )}
        </Button>
      </form>

      <div className="text-center text-sm">
        Already have an account?{" "}
        <button
          type="button"
          onClick={onSwitchToLogin}
          className="text-blue-600 hover:underline font-medium"
        >
          Sign in
        </button>
      </div>
    </div>
  );
}
