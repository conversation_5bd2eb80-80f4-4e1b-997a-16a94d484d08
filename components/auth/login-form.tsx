"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { setCookie } from "cookies-next";
import { Eye, EyeOff } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/ui/loader";
import { 
  signInWithEmail, 
  resetPassword, 
  isValidEmail 
} from "@/lib/auth-utils";

interface LoginFormProps {
  onSwitchToRegister: () => void;
}

export function LoginForm({ onSwitchToRegister }: LoginFormProps) {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!email || !isValidEmail(email)) {
      newErrors.push("Please enter a valid email address");
    }

    if (!password) {
      newErrors.push("Password is required");
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors([]);

    try {
      const { user, success, error } = await signInWithEmail(email, password);
      
      if (success && user) {
        // Check if email is verified
        if (!user.emailVerified) {
          setErrors(["Please verify your email address before signing in. Check your inbox for the verification link."]);
          setIsSubmitting(false);
          return;
        }

        const token = await user.getIdToken();
        setCookie("firebaseIdToken", token, { maxAge: 60 * 60 * 24 }); // 24 hours
        
        toast.success("Signed in successfully");
        router.push("/dashboard");
      } else {
        setErrors([error || "Failed to sign in"]);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setErrors([errorMessage]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !isValidEmail(email)) {
      setErrors(["Please enter a valid email address"]);
      return;
    }

    setIsResettingPassword(true);
    setErrors([]);

    try {
      const { success, error } = await resetPassword(email);
      
      if (success) {
        toast.success("Password reset email sent! Check your inbox");
        setShowForgotPassword(false);
      } else {
        setErrors([error || "Failed to send password reset email"]);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Failed to send password reset email";
      setErrors([errorMessage]);
    } finally {
      setIsResettingPassword(false);
    }
  };

  if (showForgotPassword) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Reset Password</h3>
          <p className="text-sm text-gray-600 mt-1">
            Enter your email address and we&apos;ll send you a link to reset your password
          </p>
        </div>

        <form onSubmit={handleForgotPassword} className="space-y-4">
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <ul className="list-disc pl-5 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="resetEmail">Email</Label>
            <Input
              id="resetEmail"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isResettingPassword}
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isResettingPassword}
          >
            {isResettingPassword ? (
              <Loader text="Sending reset email..." />
            ) : (
              "Send Reset Email"
            )}
          </Button>

          <div className="text-center">
            <button
              type="button"
              onClick={() => setShowForgotPassword(false)}
              className="text-sm text-blue-600 hover:underline"
            >
              Back to Sign In
            </button>
          </div>
        </form>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900">Sign In</h3>
        <p className="text-sm text-gray-600 mt-1">
          Welcome back! Please sign in to your account
        </p>
      </div>

      <form onSubmit={handleLogin} className="space-y-4">
        {errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <ul className="list-disc pl-5 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isSubmitting}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Loader text="Signing in..." />
          ) : (
            "Sign In"
          )}
        </Button>

        <div className="text-center">
          <button
            type="button"
            onClick={() => setShowForgotPassword(true)}
            className="text-sm text-blue-600 hover:underline"
          >
            Forgot password?
          </button>
        </div>
      </form>

      <div className="text-center text-sm">
        New here?{" "}
        <button
          type="button"
          onClick={onSwitchToRegister}
          className="text-blue-600 hover:underline font-medium"
        >
          Create an account
        </button>
      </div>
    </div>
  );
}
