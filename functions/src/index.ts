import * as functions from "firebase-functions";
import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Cloud Function to create user document when a new user is created
export const createNewUserDocument = functions.auth.user().onCreate(async (user) => {
  const { uid, email, displayName, photoURL } = user;
  const db = admin.firestore();

  try {
    // Create user document in the 'users' collection
    await db.collection("users").doc(uid).set({
      uid: uid,
      email: email || "",
      displayName: displayName || "",
      photoURL: photoURL || "",
      emailVerified: user.emailVerified || false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      plan: "free",
      stripeCustomerId: null,
      customDomain: null
    });

    console.log(`User document created for ${uid}`);
    return { success: true };
  } catch (error) {
    console.error("Error creating user document:", error);
    throw new functions.https.HttpsError("internal", "Failed to create user document");
  }
});

// Cloud Function to clean up user data when a user is deleted
export const deleteUserData = functions.auth.user().onDelete(async (user) => {
  const { uid } = user;
  const db = admin.firestore();

  try {
    // Delete user document
    await db.collection("users").doc(uid).delete();
    
    // Delete portfolio document if it exists
    const portfolioRef = db.collection("portfolios").doc(uid);
    const portfolioDoc = await portfolioRef.get();
    if (portfolioDoc.exists) {
      await portfolioRef.delete();
    }

    console.log(`User data cleaned up for ${uid}`);
    return { success: true };
  } catch (error) {
    console.error("Error cleaning up user data:", error);
    // Don't throw error here as user is already deleted from Auth
    return { success: false, error: error };
  }
});

// Cloud Function to update user document when user data changes
export const updateUserDocument = functions.auth.user().onUpdate(async (change) => {
  const { uid } = change.after;
  const db = admin.firestore();

  try {
    const userRef = db.collection("users").doc(uid);
    const updateData: any = {};

    // Check what changed and update accordingly
    if (change.before.email !== change.after.email) {
      updateData.email = change.after.email || "";
    }

    if (change.before.displayName !== change.after.displayName) {
      updateData.displayName = change.after.displayName || "";
    }

    if (change.before.photoURL !== change.after.photoURL) {
      updateData.photoURL = change.after.photoURL || "";
    }

    if (change.before.emailVerified !== change.after.emailVerified) {
      updateData.emailVerified = change.after.emailVerified;
    }

    // Only update if there are changes
    if (Object.keys(updateData).length > 0) {
      updateData.updatedAt = admin.firestore.FieldValue.serverTimestamp();
      await userRef.update(updateData);
      console.log(`User document updated for ${uid}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating user document:", error);
    // Don't throw error as this is a background function
    return { success: false, error: error };
  }
});
